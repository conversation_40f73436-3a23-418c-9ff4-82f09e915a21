<template>
  <div class="test-console-rdp">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>Console和RDP功能测试</h1>
      <p>测试Console连接和RDP连接的基本功能</p>
    </div>

    <!-- 功能状态检查 -->
    <div class="status-section">
      <h2>功能状态检查</h2>
      <div class="status-grid">
        <div class="status-card">
          <div class="status-title">Console功能</div>
          <div
            class="status-value"
            :class="{ enabled: consoleEnabled, disabled: !consoleEnabled }"
          >
            {{ consoleEnabled ? '已启用' : '已禁用' }}
          </div>
        </div>
        <div class="status-card">
          <div class="status-title">RDP功能</div>
          <div
            class="status-value"
            :class="{ enabled: rdpEnabled, disabled: !rdpEnabled }"
          >
            {{ rdpEnabled ? '已启用' : '已禁用' }}
          </div>
        </div>
        <div class="status-card">
          <div class="status-title">串口连接</div>
          <div
            class="status-value"
            :class="{ enabled: serialEnabled, disabled: !serialEnabled }"
          >
            {{ serialEnabled ? '已启用' : '已禁用' }}
          </div>
        </div>
        <div class="status-card">
          <div class="status-title">Telnet连接</div>
          <div
            class="status-value"
            :class="{ enabled: telnetEnabled, disabled: !telnetEnabled }"
          >
            {{ telnetEnabled ? '已启用' : '已禁用' }}
          </div>
        </div>
      </div>
    </div>

    <!-- Console连接测试 -->
    <div class="test-section">
      <h2>Console连接测试</h2>
      <div class="test-form">
        <div class="form-row">
          <label>连接类型:</label>
          <select v-model="consoleConfig.type">
            <option value="telnet">Telnet</option>
            <option value="serial">串口</option>
            <option value="ssh">SSH</option>
          </select>
        </div>
        <div
          v-if="consoleConfig.type !== 'serial'"
          class="form-row"
        >
          <label>主机地址:</label>
          <input
            v-model="consoleConfig.host"
            placeholder="***********"
          />
        </div>
        <div
          v-if="consoleConfig.type === 'serial'"
          class="form-row"
        >
          <label>串口设备:</label>
          <input
            v-model="consoleConfig.device"
            placeholder="/dev/ttyUSB0"
          />
        </div>
        <div
          v-if="consoleConfig.type !== 'serial'"
          class="form-row"
        >
          <label>端口:</label>
          <input
            v-model="consoleConfig.port"
            type="number"
            :placeholder="consoleConfig.type === 'telnet' ? '23' : '22'"
          />
        </div>
        <div
          v-if="consoleConfig.type === 'serial'"
          class="form-row"
        >
          <label>波特率:</label>
          <select v-model="consoleConfig.baudRate">
            <option value="9600">9600</option>
            <option value="19200">19200</option>
            <option value="38400">38400</option>
            <option value="57600">57600</option>
            <option value="115200">115200</option>
          </select>
        </div>
        <div class="form-actions">
          <button
            :disabled="consoleConnecting"
            @click="testConsoleConnection"
          >
            {{ consoleConnecting ? '连接中...' : '测试Console连接' }}
          </button>
        </div>
      </div>
      <div
        v-if="consoleResult"
        class="test-result"
      >
        <h3>Console连接结果:</h3>
        <pre>{{ JSON.stringify(consoleResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- RDP连接测试 -->
    <div class="test-section">
      <h2>RDP连接测试</h2>
      <div class="test-form">
        <div class="form-row">
          <label>主机地址:</label>
          <input
            v-model="rdpConfig.host"
            placeholder="***********00"
          />
        </div>
        <div class="form-row">
          <label>端口:</label>
          <input
            v-model="rdpConfig.port"
            type="number"
            placeholder="3389"
          />
        </div>
        <div class="form-row">
          <label>用户名:</label>
          <input
            v-model="rdpConfig.username"
            placeholder="administrator"
          />
        </div>
        <div class="form-row">
          <label>密码:</label>
          <input
            v-model="rdpConfig.password"
            type="password"
            placeholder="password"
          />
        </div>
        <div class="form-row">
          <label>域:</label>
          <input
            v-model="rdpConfig.domain"
            placeholder="WORKGROUP"
          />
        </div>
        <div class="form-actions">
          <button
            :disabled="rdpConnecting"
            @click="testRdpConnection"
          >
            {{ rdpConnecting ? '连接中...' : '测试RDP连接' }}
          </button>
        </div>
      </div>
      <div
        v-if="rdpResult"
        class="test-result"
      >
        <h3>RDP连接结果:</h3>
        <pre>{{ JSON.stringify(rdpResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- 连接配置测试 -->
    <div class="test-section">
      <h2>连接配置测试</h2>
      <div class="config-actions">
        <button @click="getConnectionConfig">获取连接配置</button>
        <button @click="resetConnectionConfig">重置配置</button>
      </div>
      <div
        v-if="configResult"
        class="test-result"
      >
        <h3>配置信息:</h3>
        <pre>{{ JSON.stringify(configResult, null, 2) }}</pre>
      </div>
    </div>

    <!-- 活动连接列表 -->
    <div class="test-section">
      <h2>活动连接列表</h2>
      <div class="config-actions">
        <button @click="getActiveConnections">获取活动连接</button>
      </div>
      <div
        v-if="connectionsResult"
        class="test-result"
      >
        <h3>活动连接:</h3>
        <pre>{{ JSON.stringify(connectionsResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 功能状态
const consoleEnabled = ref(false)
const rdpEnabled = ref(false)
const serialEnabled = ref(false)
const telnetEnabled = ref(false)

// Console配置
const consoleConfig = ref({
  type: 'telnet',
  host: '***********',
  port: 23,
  device: '/dev/ttyUSB0',
  baudRate: 9600
})

// RDP配置
const rdpConfig = ref({
  host: '***********00',
  port: 3389,
  username: 'administrator',
  password: '',
  domain: 'WORKGROUP'
})

// 测试状态
const consoleConnecting = ref(false)
const rdpConnecting = ref(false)

// 测试结果
const consoleResult = ref(null)
const rdpResult = ref(null)
const configResult = ref(null)
const connectionsResult = ref(null)

/**
 * 检查功能状态
 */
const checkFeatureStatus = async () => {
  try {
    const api = window.api as any
    if (api) {
      consoleEnabled.value = await api.isConsoleEnabled()
      rdpEnabled.value = await api.isRdpEnabled()
      serialEnabled.value = await api.isSerialEnabled()
      telnetEnabled.value = await api.isTelnetEnabled()
    }
  } catch (error) {
    console.error('检查功能状态失败:', error)
  }
}

/**
 * 测试Console连接
 */
const testConsoleConnection = async () => {
  consoleConnecting.value = true
  consoleResult.value = null

  try {
    const api = window.api as any
    if (!api || !api.consoleConnect) {
      throw new Error('Console API不可用')
    }

    const config = {
      id: `test-console-${Date.now()}`,
      type: consoleConfig.value.type,
      ...(consoleConfig.value.type === 'serial'
        ? {
            device: consoleConfig.value.device,
            baudRate: consoleConfig.value.baudRate,
            dataBits: 8,
            stopBits: 1,
            parity: 'none'
          }
        : {
            host: consoleConfig.value.host,
            port: consoleConfig.value.port,
            timeout: 5000
          })
    }

    const result = await api.consoleConnect(config)
    consoleResult.value = result
  } catch (error) {
    consoleResult.value = {
      success: false,
      error: error.message || '连接失败'
    }
  } finally {
    consoleConnecting.value = false
  }
}

/**
 * 测试RDP连接
 */
const testRdpConnection = async () => {
  rdpConnecting.value = true
  rdpResult.value = null

  try {
    const api = window.api as any
    if (!api || !api.rdpConnect) {
      throw new Error('RDP API不可用')
    }

    const config = {
      id: `test-rdp-${Date.now()}`,
      host: rdpConfig.value.host,
      port: rdpConfig.value.port,
      username: rdpConfig.value.username,
      password: rdpConfig.value.password,
      domain: rdpConfig.value.domain,
      displaySettings: {
        width: 1920,
        height: 1080,
        colorDepth: 32
      }
    }

    const result = await api.rdpConnect(config)
    rdpResult.value = result
  } catch (error) {
    rdpResult.value = {
      success: false,
      error: error.message || '连接失败'
    }
  } finally {
    rdpConnecting.value = false
  }
}

/**
 * 获取连接配置
 */
const getConnectionConfig = async () => {
  try {
    const api = window.api as any
    if (api && api.getConnectionConfig) {
      const result = await api.getConnectionConfig()
      configResult.value = result
    }
  } catch (error) {
    configResult.value = {
      success: false,
      error: error.message || '获取配置失败'
    }
  }
}

/**
 * 重置连接配置
 */
const resetConnectionConfig = async () => {
  try {
    const api = window.api as any
    if (api && api.resetConnectionConfig) {
      const result = await api.resetConnectionConfig()
      configResult.value = result
      // 重新检查功能状态
      await checkFeatureStatus()
    }
  } catch (error) {
    configResult.value = {
      success: false,
      error: error.message || '重置配置失败'
    }
  }
}

/**
 * 获取活动连接
 */
const getActiveConnections = async () => {
  try {
    const api = window.api as any
    if (api && api.connectionsList) {
      const result = await api.connectionsList()
      connectionsResult.value = result
    }
  } catch (error) {
    connectionsResult.value = {
      success: false,
      error: error.message || '获取连接列表失败'
    }
  }
}

// 组件挂载时检查功能状态
onMounted(() => {
  checkFeatureStatus()
})
</script>

<style scoped>
.test-console-rdp {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-header h1 {
  font-size: 28px;
  color: #1890ff;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.status-section {
  margin-bottom: 40px;
}

.status-section h2 {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.status-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.status-value {
  font-size: 18px;
  font-weight: bold;
}

.status-value.enabled {
  color: #52c41a;
}

.status-value.disabled {
  color: #ff4d4f;
}

.test-section {
  margin-bottom: 40px;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.test-section h2 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 10px;
}

.test-form {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.form-row label {
  width: 120px;
  font-weight: 500;
  color: #333;
}

.form-row input,
.form-row select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.form-row input:focus,
.form-row select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-actions,
.config-actions {
  margin-top: 20px;
}

.form-actions button,
.config-actions button {
  background: #1890ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;
}

.form-actions button:hover,
.config-actions button:hover {
  background: #40a9ff;
}

.form-actions button:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.test-result {
  margin-top: 20px;
  padding: 15px;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
}

.test-result h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #333;
}

.test-result pre {
  margin: 0;
  padding: 10px;
  background: #fff;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
