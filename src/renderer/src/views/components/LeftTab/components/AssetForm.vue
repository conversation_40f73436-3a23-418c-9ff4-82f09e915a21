<template>
  <div class="asset-form-container">
    <div class="form-header">
      <div style="font-size: 14px; font-weight: bold">
        <h3>{{ isEditMode ? t('personal.editHost') : t('personal.newHost') }}</h3>
      </div>
      <ToTopOutlined
        style="font-size: 20px; transform: rotate(90deg); cursor: pointer"
        class="close-icon"
        @click="handleClose"
      />
    </div>

    <div class="form-content">
      <a-form
        :label-col="{ span: 27 }"
        :wrapper-col="{ span: 27 }"
        layout="vertical"
        class="custom-form"
      >
        <!-- 资产类型选择 -->
        <a-form-item v-if="!isEditMode">
          <a-radio-group
            v-model:value="formData.asset_type"
            button-style="solid"
            style="width: 100%"
          >
            <a-radio-button value="person">{{ t('personal.personalAsset') }}</a-radio-button>
            <a-radio-button value="organization">
              <a-tooltip :title="t('personal.organizationTip')">
                {{ t('personal.enterpriseAsset') }}
              </a-tooltip>
            </a-radio-button>
          </a-radio-group>
        </a-form-item>

        <!-- 连接类型选择 -->
        <div class="form-section">
          <div class="section-title">
            <div class="title-indicator"></div>
            连接类型
          </div>

          <a-form-item label="连接方式">
            <a-radio-group
              v-model:value="formData.connection_type"
              button-style="solid"
              style="width: 100%"
              @change="handleConnectionTypeChange"
            >
              <a-radio-button value="ssh">
                <DesktopOutlined style="margin-right: 4px" />
                SSH
              </a-radio-button>
              <a-radio-button value="console">
                <CodeOutlined style="margin-right: 4px" />
                Console（串口）
              </a-radio-button>
              <a-radio-button value="telnet">
                <GlobalOutlined style="margin-right: 4px" />
                Telnet
              </a-radio-button>
              <a-radio-button value="rdp">
                <WindowsOutlined style="margin-right: 4px" />
                RDP
              </a-radio-button>
            </a-radio-group>
          </a-form-item>
        </div>

        <!-- 连接信息 -->
        <div 
          v-if="formData.connection_type !== 'console'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            连接信息
          </div>

          <a-form-item label="连接IP或地址">
            <a-input
              v-model:value="formData.ip"
              placeholder="请输入远程连接地址"
            />
          </a-form-item>

          <a-form-item label="端口">
            <a-input
              v-model:value="formData.port"
              :min="20"
              :max="65536"
              placeholder="请输入端口"
              style="width: 100%"
            />
          </a-form-item>
        </div>

        <!-- Console连接配置（串口） -->
        <div
          v-if="formData.connection_type === 'console'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            Console连接配置（串口）
          </div>

          <!-- 串口配置 -->
          <a-form-item label="设备路径">
              <div style="display: flex; gap: 8px">
                <a-select
                  v-model:value="formData.serial_config.device"
                  placeholder="选择或输入设备路径"
                  style="flex: 1"
                  show-search
                  allow-clear
                  :options="availableSerialDevices"
                  :loading="detectingDevices"
                  @dropdown-visible-change="onDeviceDropdownChange"
                >
                  <template #notFoundContent>
                    <div style="text-align: center; padding: 8px">
                      <div v-if="detectingDevices">正在检测设备...</div>
                      <div v-else>未找到可用设备</div>
                    </div>
                  </template>
                </a-select>
                <a-button
                  type="default"
                  :loading="detectingDevices"
                  @click="detectSerialDevices"
                >
                  刷新
                </a-button>
              </div>
            </a-form-item>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="波特率">
                  <a-select
                    v-model:value="formData.serial_config.baudRate"
                    placeholder="选择波特率"
                  >
                    <a-select-option :value="1200">1200</a-select-option>
                    <a-select-option :value="2400">2400</a-select-option>
                    <a-select-option :value="4800">4800</a-select-option>
                    <a-select-option :value="9600">9600</a-select-option>
                    <a-select-option :value="19200">19200</a-select-option>
                    <a-select-option :value="38400">38400</a-select-option>
                    <a-select-option :value="57600">57600</a-select-option>
                    <a-select-option :value="115200">115200</a-select-option>
                    <a-select-option :value="230400">230400</a-select-option>
                    <a-select-option :value="460800">460800</a-select-option>
                    <a-select-option :value="921600">921600</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="数据位">
                  <a-select
                    v-model:value="formData.serial_config.dataBits"
                    placeholder="数据位"
                  >
                    <a-select-option :value="5">5</a-select-option>
                    <a-select-option :value="6">6</a-select-option>
                    <a-select-option :value="7">7</a-select-option>
                    <a-select-option :value="8">8</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="停止位">
                  <a-select
                    v-model:value="formData.serial_config.stopBits"
                    placeholder="停止位"
                  >
                    <a-select-option :value="1">1</a-select-option>
                    <a-select-option :value="1.5">1.5</a-select-option>
                    <a-select-option :value="2">2</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="校验位">
                  <a-select
                    v-model:value="formData.serial_config.parity"
                    placeholder="校验位"
                  >
                    <a-select-option value="none">无 (None)</a-select-option>
                    <a-select-option value="even">偶校验 (Even)</a-select-option>
                    <a-select-option value="odd">奇校验 (Odd)</a-select-option>
                    <a-select-option value="mark">标记 (Mark)</a-select-option>
                    <a-select-option value="space">空格 (Space)</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="流控制">
                  <a-select
                    v-model:value="formData.serial_config.flowControl"
                    placeholder="流控制"
                  >
                    <a-select-option value="none">无 (None)</a-select-option>
                    <a-select-option value="rts_cts">RTS/CTS</a-select-option>
                    <a-select-option value="xon_xoff">XON/XOFF</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
        </div>

        <!-- Telnet连接配置 -->
        <div
          v-if="formData.connection_type === 'telnet'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            Telnet连接配置
          </div>

          <a-form-item label="超时时间(秒)">
            <a-input-number
              v-model:value="formData.telnet_config.timeout"
              :min="1"
              :max="300"
              placeholder="连接超时时间"
              style="width: 100%"
            />
          </a-form-item>
        </div>

        <!-- RDP连接配置 -->
        <div
          v-if="formData.connection_type === 'rdp'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            RDP连接配置
          </div>

          <a-form-item label="域名">
            <a-input
              v-model:value="formData.rdp_config.domain"
              placeholder="例如: WORKGROUP 或域名"
            />
          </a-form-item>

          <div
            class="section-title"
            style="margin-top: 16px"
          >
            <div class="title-indicator"></div>
            显示设置
          </div>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="宽度">
                <a-input-number
                  v-model:value="formData.rdp_config.display_settings.width"
                  :min="800"
                  :max="3840"
                  placeholder="1920"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="高度">
                <a-input-number
                  v-model:value="formData.rdp_config.display_settings.height"
                  :min="600"
                  :max="2160"
                  placeholder="1080"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="色深">
                <a-select
                  v-model:value="formData.rdp_config.display_settings.colorDepth"
                  placeholder="色深"
                >
                  <a-select-option :value="16">16位</a-select-option>
                  <a-select-option :value="24">24位</a-select-option>
                  <a-select-option :value="32">32位</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <div
            class="section-title"
            style="margin-top: 16px"
          >
            <div class="title-indicator"></div>
            安全设置
          </div>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="启用加密">
                <a-switch v-model:checked="formData.rdp_config.security_settings.encryption" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="认证方式">
                <a-select
                  v-model:value="formData.rdp_config.security_settings.authentication"
                  placeholder="认证方式"
                >
                  <a-select-option value="ntlm">NTLM</a-select-option>
                  <a-select-option value="basic">基本认证</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 认证信息 -->
        <div
          v-if="!isConsoleConnection"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            认证信息
          </div>

          <a-form-item
            v-if="formData.asset_type === 'person'"
            :label="t('personal.verificationMethod')"
          >
            <a-radio-group
              v-model:value="formData.auth_type"
              button-style="solid"
              style="width: 100%"
              @change="handleAuthChange"
            >
              <a-radio-button value="password">{{ t('personal.password') }}</a-radio-button>
              <a-radio-button value="keyBased">{{ t('personal.key') }}</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <a-form-item :label="t('personal.username')">
            <a-input
              v-model:value="formData.username"
              :placeholder="t('personal.pleaseInputUsername')"
            />
          </a-form-item>

          <a-form-item
            v-if="formData.auth_type == 'password'"
            :label="t('personal.password')"
          >
            <a-input-password
              v-model:value="formData.password"
              :placeholder="t('personal.pleaseInputPassword')"
            />
          </a-form-item>

          <template v-if="formData.auth_type === 'keyBased'">
            <a-form-item :label="t('personal.key')">
              <a-select
                v-model:value="formData.keyChain"
                :placeholder="t('personal.pleaseSelectKeychain')"
                style="width: 100%"
                show-search
                :max-tag-count="4"
                :options="keyChainOptions"
                :option-filter-prop="'label'"
                :field-names="{ value: 'key', label: 'label' }"
                :allow-clear="true"
              >
                <template #notFoundContent>
                  <div style="text-align: center; width: 100%">
                    <a-button
                      type="link"
                      @click="handleAddKeychain"
                      >{{ t('keyChain.newKey') }}</a-button
                    >
                  </div>
                </template>
              </a-select>
            </a-form-item>

            <a-form-item
              v-if="formData.asset_type === 'organization'"
              :label="t('personal.password')"
            >
              <a-input-password
                v-model:value="formData.password"
                :placeholder="t('personal.pleaseInputPassword')"
              />
            </a-form-item>
          </template>

          <div>
            <a-form-item
              :label="t('personal.proxyConfig')"
              class="user_my-ant-form-item"
            >
              <a-switch
                :checked="formData.needProxy"
                class="user_my-ant-form-item-content"
                @change="handleSshProxyStatusChange"
              />
            </a-form-item>

            <a-form-item
              v-if="formData.needProxy"
              :label="t('personal.pleaseSelectSshProxy')"
            >
              <a-select
                v-model:value="formData.proxyName"
                :placeholder="t('personal.pleaseSelectSshProxy')"
                style="width: 100%"
                show-search
                :max-tag-count="4"
                :options="sshProxyConfigs"
                :option-filter-prop="'label'"
                :field-names="{ value: 'key', label: 'label' }"
                :allow-clear="true"
              >
              </a-select>
            </a-form-item>
          </div>
        </div>

        <!-- 通用信息 -->
        <div class="form-section">
          <div class="section-title">
            <div class="title-indicator"></div>
            {{ t('personal.general') }}
          </div>

          <a-form-item :label="t('personal.alias')">
            <a-input
              v-model:value="formData.label"
              :placeholder="t('personal.pleaseInputAlias')"
            />
          </a-form-item>

          <a-form-item
            :label="t('personal.group')"
            class="general-group"
          >
            <a-select
              v-model:value="formData.group_name"
              mode="tags"
              :placeholder="t('personal.pleaseSelectGroup')"
              :max-tag-count="2"
              style="width: 100%"
              @change="handleGroupChange"
            >
              <a-select-option
                v-for="item in defaultGroups"
                :key="item"
                :value="item"
              >
                {{ item }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </a-form>
    </div>

    <div class="form-footer">
      <a-button
        type="primary"
        class="submit-button"
        @click="handleSubmit"
      >
        {{ isEditMode ? t('personal.saveAsset') : t('personal.createAsset') }}
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref, computed } from 'vue'
import { ToTopOutlined, DesktopOutlined, CodeOutlined, WindowsOutlined, GlobalOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import i18n from '@/locales'
import type { AssetFormData, KeyChainItem, sshProxyConfig } from '../types'
import { SshProxyConfigItem } from '../types'

const { t } = i18n.global

// Props
interface Props {
  isEditMode?: boolean
  initialData?: Partial<AssetFormData>
  keyChainOptions?: KeyChainItem[]
  sshProxyConfigs?: SshProxyConfigItem[]
  defaultGroups?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  isEditMode: false,
  initialData: () => ({}),
  keyChainOptions: () => [],
  sshProxyConfigs: () => [],
  defaultGroups: () => ['development', 'production', 'staging', 'testing', 'database']
})

// Emits
const emit = defineEmits<{
  close: []
  submit: [data: AssetFormData]
  'add-keychain': []
  'auth-change': [authType: string]
}>()

// State
const formData = reactive<AssetFormData>({
  username: '',
  password: '',
  ip: '',
  label: '',
  group_name: ['Hosts'], // 初始化为数组类型
  auth_type: 'password',
  keyChain: undefined,
  port: 22,
  asset_type: 'person',
  needProxy: false,
  proxyName: '',
  // Console和RDP连接类型支持
  connection_type: 'ssh',
  serial_config: {
    baudRate: 9600,
    dataBits: 8,
    stopBits: 1,
    parity: 'none',
    device: '',
    flowControl: 'none'
  },
  telnet_config: {
    timeout: 30
  },
  ssh_config: {
    timeout: 30
  },
  rdp_config: {
    domain: 'WORKGROUP',
    display_settings: {
      width: 1920,
      height: 1080,
      colorDepth: 32
    },
    security_settings: {
      encryption: true,
      authentication: 'ntlm'
    }
  },
  ...props.initialData
})

// 设备检测相关状态
const detectingDevices = ref(false)
const availableSerialDevices = ref<Array<{ label: string; value: string }>>([])

// 计算属性：判断是否为Console连接（串口连接不需要认证信息）
const isConsoleConnection = computed(() => {
  return formData.connection_type === 'console'
})

// 在组件挂载后设置默认组名
watch(
  () => props.initialData,
  (newData) => {
    if (!newData?.group_name) {
      formData.group_name = [t('personal.defaultGroup')]
    } else if (typeof newData.group_name === 'string') {
      formData.group_name = [newData.group_name]
    }
  },
  { immediate: true }
)

// Watch for asset type changes
watch(
  () => formData.asset_type,
  (newAssetType) => {
    if (newAssetType === 'organization') {
      formData.auth_type = 'keyBased'
    } else {
      formData.auth_type = 'password'
    }
  }
)

// Watch for edit mode and asset type
watch(
  [() => props.isEditMode, () => formData.asset_type],
  ([editing, assetType]) => {
    if (editing && assetType === 'organization') {
      formData.auth_type = 'keyBased'
    }
  },
  { immediate: true }
)

// Methods
const handleClose = () => {
  emit('close')
}

const handleAuthChange = () => {
  if (formData.auth_type === 'keyBased') {
    emit('auth-change', 'keyBased')
  }
  if (formData.auth_type === 'password') {
    formData.keyChain = undefined
  } else {
    formData.password = ''
  }
}

const handleAddKeychain = () => {
  emit('add-keychain')
}

const handleGroupChange = (val: any) => {
  if (Array.isArray(val)) {
    formData.group_name = val
  } else if (typeof val === 'string') {
    formData.group_name = [val]
  } else {
    formData.group_name = []
  }
}

const validateForm = (): boolean => {
  // 企业资产校验
  if (formData.asset_type === 'organization') {
    if (!formData.ip || !formData.ip.trim()) {
      message.error(t('personal.validationRemoteHostRequired'))
      return false
    }
    if (!formData.port || formData.port <= 0) {
      message.error(t('personal.validationPortRequired'))
      return false
    }
    if (!formData.username || !formData.username.trim()) {
      message.error(t('personal.validationUsernameRequired'))
      return false
    }
    if (formData.auth_type === 'keyBased' && !formData.keyChain) {
      message.error(t('personal.validationKeychainRequired'))
      return false
    }
  }
  return true
}

const handleSubmit = () => {
  if (!validateForm()) return
  emit('submit', { ...formData })
}

const handleSshProxyStatusChange = async (checked) => {
  formData.needProxy = checked
}

// 设备检测相关方法
const detectSerialDevices = async () => {
  detectingDevices.value = true
  try {
    // 根据操作系统检测可用的串口设备
    const devices: Array<{ label: string; value: string }> = []

    // macOS/Linux 常见串口设备
    const unixDevices = [
      '/dev/ttyUSB0',
      '/dev/ttyUSB1',
      '/dev/ttyUSB2',
      '/dev/ttyUSB3',
      '/dev/ttyACM0',
      '/dev/ttyACM1',
      '/dev/ttyACM2',
      '/dev/ttyACM3',
      '/dev/ttyS0',
      '/dev/ttyS1',
      '/dev/ttyS2',
      '/dev/ttyS3',
      '/dev/cu.usbserial',
      '/dev/cu.usbmodem'
    ]

    // Windows 常见串口设备
    const windowsDevices = ['COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8']

    // 检测当前操作系统
    const isWindows = navigator.platform.toLowerCase().includes('win')
    const deviceList = isWindows ? windowsDevices : unixDevices

    // 模拟设备检测（实际项目中可以通过API调用系统命令检测）
    for (const device of deviceList) {
      devices.push({
        label: `${device} (串口设备)`,
        value: device
      })
    }

    // 添加一些常用的网络设备端口
    if (!isWindows) {
      devices.push({ label: '/dev/ttyUSB0 (USB转串口)', value: '/dev/ttyUSB0' }, { label: '/dev/ttyACM0 (Arduino设备)', value: '/dev/ttyACM0' })
    }

    availableSerialDevices.value = devices
  } catch (error) {
    console.error('检测串口设备失败:', error)
    message.error('检测串口设备失败')
  } finally {
    detectingDevices.value = false
  }
}

// 处理设备下拉框显示事件
const onDeviceDropdownChange = (visible: boolean) => {
  if (visible && availableSerialDevices.value.length === 0) {
    detectSerialDevices()
  }
}

// 处理连接类型变化
const handleConnectionTypeChange = () => {
  // 根据连接类型设置默认端口
  switch (formData.connection_type) {
    case 'ssh':
      formData.port = 22
      break
    case 'console':
      formData.port = 0 // 串口不需要端口
      // 确保serial_config已初始化
      if (!formData.serial_config) {
        formData.serial_config = {
          baudRate: 9600,
          dataBits: 8,
          stopBits: 1,
          parity: 'none',
          device: '',
          flowControl: 'none'
        }
      }
      break
    case 'telnet':
      formData.port = 23
      break
    case 'rdp':
      formData.port = 3389
      break
  }
}

// Reset form data when initialData changes
watch(
  () => props.initialData,
  (newData) => {
    const defaultGroupName = t('personal.defaultGroup')
    Object.assign(formData, {
      username: '',
      password: '',
      ip: '',
      label: '',
      group_name: defaultGroupName,
      auth_type: 'password',
      keyChain: undefined,
      port: 22,
      asset_type: 'person',
      needProxy: false,
      proxyName: '',
      connection_type: 'ssh',
      serial_config: {
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
        device: '',
        flowControl: 'none'
      },
      telnet_config: {
        timeout: 30
      },
      ssh_config: {
        timeout: 30
      },
      rdp_config: {
        domain: 'WORKGROUP',
        display_settings: {
          width: 1920,
          height: 1080,
          colorDepth: 32
        },
        security_settings: {
          encryption: true,
          authentication: 'ntlm'
        }
      },
      ...newData
    })
  },
  { deep: true }
)
</script>

<style lang="less" scoped>
.asset-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-color);
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px 0 16px;
  flex-shrink: 0;
}

.close-icon {
  &:hover {
    color: #52c41a;
    transition: color 0.3s;
  }
}

.form-content {
  flex: 1;
  padding: 12px 16px 0 16px;
  overflow: auto;
  color: var(--text-color);
}

.form-footer {
  padding: 12px 16px;
  flex-shrink: 0;
}

.submit-button {
  width: 100%;
  height: 36px;
  border-radius: 4px;
  background-color: #1890ff;
  border-color: #1890ff;

  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }
}

.form-section {
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: var(--text-color);
}

.title-indicator {
  width: 2px;
  height: 12px;
  background: #1677ff;
  margin-right: 4px;
}

.custom-form {
  color: var(--text-color);

  :deep(.ant-form-item) {
    margin-bottom: 12px;
  }

  :deep(.ant-form-item-label) {
    min-width: 250px;
    padding-bottom: 4px;

    > label {
      color: var(--text-color);
    }
  }
}

:deep(.ant-form-item) {
  color: var(--text-color-secondary);
}

:global(.light-theme) {
  .custom-form {
    :deep(.ant-form-item-label > label) {
      color: rgba(0, 0, 0, 0.85) !important;
    }
  }
}

.custom-form :deep(.ant-input),
.custom-form :deep(.ant-input-password),
.custom-form :deep(.ant-select-selector),
.custom-form :deep(.ant-input-number-input) {
  color: var(--text-color);
  background-color: var(--component-bg-color, #ffffff) !important;
  border-color: var(--border-color) !important;

  &::placeholder {
    color: var(--text-color-tertiary);
  }
}

/* 特别针对Console设备路径选择框的背景色 */
.custom-form :deep(.ant-select-selector) {
  background-color: var(--component-bg-color, #ffffff) !important;
  opacity: 1 !important;
}

:global(.dark-theme) .custom-form :deep(.ant-select-selector) {
  background-color: var(--component-bg-color, #1f1f1f) !important;
}

.custom-form :deep(.ant-select-selection-placeholder) {
  color: var(--text-color-tertiary);
}

.custom-form :deep(.ant-radio-button-wrapper) {
  background: var(--bg-color) !important;
  color: var(--text-color);

  .ant-radio-button-checked {
    border: #1677ff;
  }
}

.custom-form :deep(.ant-radio-button-wrapper-checked) {
  color: #1677ff;
}

.custom-form :deep(.ant-select-selector),
.custom-form :deep(.anticon.ant-input-password-icon),
.custom-form :deep(.ant-select-arrow) {
  color: var(--text-color);
}

.general-group :deep(.ant-select-selection-item) {
  background-color: var(--hover-bg-color);
}

.custom-form :deep(.ant-select-selection-item) {
  color: var(--text-color) !important;
  background-color: transparent !important;
}
</style>
