/**
 * 统一连接管理器
 * 整合Console和RDP连接服务，提供统一的连接管理接口
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { EventEmitter } from 'events'
import { ConsoleManager } from '../console/consoleManager'
import { ConsoleConnectionConfig, ConsoleConnectionType } from '../console/types'
import { RdpConnectionConfig } from '../rdp/types'
import { RdpService } from '../rdp/rdpService'

/**
 * 连接类型枚举
 */
export enum ConnectionType {
  CONSOLE = 'console',
  RDP = 'rdp'
}

/**
 * 统一连接配置接口
 */
export interface UnifiedConnectionConfig {
  id: string
  assetId: string
  name: string
  type: ConnectionType
  subType?: ConsoleConnectionType | 'rdp'
  config: ConsoleConnectionConfig | RdpConnectionConfig
  autoReconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
}

/**
 * 统一连接结果接口
 */
export interface UnifiedConnectionResult {
  connectionId: string
  type: ConnectionType
  status: string
  message: string
  error?: Error
  connectedAt?: Date
  sessionInfo?: any
}

/**
 * 统一连接信息接口
 */
export interface UnifiedConnectionInfo {
  id: string
  assetId: string
  name: string
  type: ConnectionType
  subType?: string
  status: string
  startedAt: Date
  lastActivityAt: Date
  reconnectCount: number
  bytesSent?: number
  bytesReceived?: number
  sessionInfo?: any
}

/**
 * 统一连接管理器选项接口
 */
export interface UnifiedConnectionManagerOptions {
  maxConnections?: number
  enableLogging?: boolean
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  cleanupInterval?: number
  consoleOptions?: any
  rdpOptions?: any
}

/**
 * 统一连接管理器类
 * 管理所有类型的连接（Console和RDP）
 */
export class UnifiedConnectionManager extends EventEmitter {
  private consoleManager: ConsoleManager
  private rdpConnections: Map<string, RdpService> = new Map()
  private options: Required<UnifiedConnectionManagerOptions>
  private cleanupTimer?: NodeJS.Timeout
  private logger: Console

  /**
   * 构造函数
   * @param options 管理器选项
   */
  constructor(options: UnifiedConnectionManagerOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      maxConnections: options.maxConnections ?? 100,
      enableLogging: options.enableLogging ?? true,
      logLevel: options.logLevel ?? 'info',
      cleanupInterval: options.cleanupInterval ?? 60000,
      consoleOptions: options.consoleOptions ?? {},
      rdpOptions: options.rdpOptions ?? {}
    }

    this.logger = console

    // 初始化Console管理器
    this.consoleManager = new ConsoleManager(this.options.consoleOptions)

    // 设置事件监听
    this.setupEventHandlers()

    // 启动定期清理
    this.startCleanupTimer()

    this.log('info', '统一连接管理器已初始化', { options: this.options })
  }

  /**
   * 建立连接
   * @param config 连接配置
   * @returns 连接结果
   */
  public async connect(config: UnifiedConnectionConfig): Promise<UnifiedConnectionResult> {
    try {
      // 检查连接数限制
      const totalConnections = await this.getTotalConnectionCount()
      if (totalConnections >= this.options.maxConnections) {
        throw new Error(`连接数已达到最大限制: ${this.options.maxConnections}`)
      }

      this.log('info', '开始建立连接', {
        id: config.id,
        type: config.type,
        assetId: config.assetId
      })

      let result: UnifiedConnectionResult

      switch (config.type) {
        case ConnectionType.CONSOLE:
          result = await this.connectConsole(config.config as ConsoleConnectionConfig)
          break
        case ConnectionType.RDP:
          result = await this.connectRdp(config.config as RdpConnectionConfig)
          break
        default:
          throw new Error(`不支持的连接类型: ${config.type}`)
      }

      this.log('info', '连接建立成功', { connectionId: config.id, type: config.type })
      this.emit('connectionEstablished', result)

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      this.log('error', '连接建立失败', {
        id: config.id,
        type: config.type,
        error: errorMessage
      })

      return {
        connectionId: config.id,
        type: config.type,
        status: 'error',
        message: `连接失败: ${errorMessage}`,
        error: error instanceof Error ? error : new Error(errorMessage)
      }
    }
  }

  /**
   * 断开连接
   * @param connectionId 连接ID
   * @param type 连接类型
   */
  public async disconnect(connectionId: string, type?: ConnectionType): Promise<void> {
    this.log('info', '断开连接', { connectionId, type })

    if (type === ConnectionType.CONSOLE || !type) {
      try {
        await this.consoleManager.disconnect(connectionId)
        this.log('info', 'Console连接已断开', { connectionId })
        return
      } catch (error) {
        if (type === ConnectionType.CONSOLE) {
          throw error
        }
        // 如果没有指定类型，继续尝试RDP
      }
    }

    if (type === ConnectionType.RDP || !type) {
      const rdpConnection = this.rdpConnections.get(connectionId)
      if (rdpConnection) {
        try {
          await rdpConnection.disconnect()
          this.rdpConnections.delete(connectionId)
          this.log('info', 'RDP连接已断开', { connectionId })
          return
        } catch (error) {
          if (type === ConnectionType.RDP) {
            throw error
          }
        }
      }
    }

    if (type) {
      throw new Error(`连接不存在: ${connectionId} (类型: ${type})`)
    } else {
      throw new Error(`连接不存在: ${connectionId}`)
    }
  }

  /**
   * 获取连接状态
   * @param connectionId 连接ID
   * @param type 连接类型（可选）
   */
  public async getConnectionStatus(connectionId: string, type?: ConnectionType) {
    if (type === ConnectionType.CONSOLE || !type) {
      try {
        const status = await this.consoleManager.getStatus(connectionId)
        return {
          ...status,
          type: ConnectionType.CONSOLE
        }
      } catch (error) {
        if (type === ConnectionType.CONSOLE) {
          throw error
        }
      }
    }

    if (type === ConnectionType.RDP || !type) {
      const rdpConnection = this.rdpConnections.get(connectionId)
      if (rdpConnection) {
        const info = rdpConnection.getConnectionInfo()
        return {
          connectionId,
          type: ConnectionType.RDP,
          status: info.status,
          info,
          isAlive: rdpConnection.isAlive()
        }
      }
    }

    return {
      connectionId,
      type: type || 'unknown',
      status: 'disconnected',
      message: '连接不存在'
    }
  }

  /**
   * 获取所有连接列表
   */
  public async getAllConnections(): Promise<{
    total: number
    console: any[]
    rdp: UnifiedConnectionInfo[]
    connections: UnifiedConnectionInfo[]
  }> {
    // 获取Console连接
    const consoleConnections = await this.consoleManager.listConnections()

    // 获取RDP连接
    const rdpConnections = Array.from(this.rdpConnections.values()).map((conn) => {
      const info = conn.getConnectionInfo()
      return {
        id: info.id,
        assetId: info.assetId,
        name: info.name,
        type: ConnectionType.RDP,
        subType: 'rdp',
        status: info.status,
        startedAt: info.startedAt,
        lastActivityAt: info.lastActivityAt,
        reconnectCount: info.reconnectCount,
        sessionInfo: info.sessionInfo
      } as UnifiedConnectionInfo
    })

    // 合并所有连接
    const allConnections: UnifiedConnectionInfo[] = [
      ...consoleConnections.connections.map((conn) => ({
        ...conn.info,
        type: ConnectionType.CONSOLE,
        subType: conn.info.type
      })),
      ...rdpConnections
    ]

    return {
      total: allConnections.length,
      console: consoleConnections.connections,
      rdp: rdpConnections,
      connections: allConnections
    }
  }

  /**
   * 清理无效连接
   */
  public async cleanup(): Promise<void> {
    this.log('info', '开始清理无效连接')

    // 清理Console连接
    await this.consoleManager.cleanup()

    // 清理RDP连接
    const deadRdpConnections: string[] = []

    for (const [id, connection] of this.rdpConnections) {
      if (!connection.isAlive()) {
        deadRdpConnections.push(id)
      }
    }

    if (deadRdpConnections.length > 0) {
      this.log('info', '清理无效RDP连接', { count: deadRdpConnections.length })

      for (const id of deadRdpConnections) {
        try {
          await this.disconnect(id, ConnectionType.RDP)
        } catch (error) {
          this.log('warn', '清理RDP连接时发生错误', {
            connectionId: id,
            error: error instanceof Error ? error.message : '未知错误'
          })
        }
      }
    }
  }

  /**
   * 关闭管理器
   */
  public async shutdown(): Promise<void> {
    this.log('info', '关闭统一连接管理器')

    // 停止清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    // 关闭Console管理器
    await this.consoleManager.shutdown()

    // 关闭所有RDP连接
    const rdpConnectionIds = Array.from(this.rdpConnections.keys())
    for (const id of rdpConnectionIds) {
      try {
        await this.disconnect(id, ConnectionType.RDP)
      } catch (error) {
        this.log('warn', '关闭RDP连接时发生错误', {
          connectionId: id,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    this.log('info', '统一连接管理器已关闭')
  }

  /**
   * 建立Console连接
   * @param config Console连接配置
   */
  private async connectConsole(config: ConsoleConnectionConfig): Promise<UnifiedConnectionResult> {
    const result = await this.consoleManager.connect(config)
    return {
      connectionId: result.connectionId,
      type: ConnectionType.CONSOLE,
      status: result.status,
      message: result.message,
      error: result.error,
      connectedAt: result.connectedAt
    }
  }

  /**
   * 建立RDP连接
   * @param config RDP连接配置
   */
  private async connectRdp(config: RdpConnectionConfig): Promise<UnifiedConnectionResult> {
    const rdpService = new RdpService(config.id, config)

    // 设置事件监听
    this.setupRdpConnectionEvents(rdpService)

    // 建立连接
    await rdpService.connect()

    // 存储连接
    this.rdpConnections.set(config.id, rdpService)

    return {
      connectionId: config.id,
      type: ConnectionType.RDP,
      status: rdpService.status,
      message: '连接建立成功',
      connectedAt: rdpService.startedAt,
      sessionInfo: rdpService.sessionInfo
    }
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // Console管理器事件
    this.consoleManager.on('connectionEstablished', (result) => {
      this.emit('connectionEstablished', {
        ...result,
        type: ConnectionType.CONSOLE
      })
    })

    this.consoleManager.on('connectionConnect', (connectionId) => {
      this.emit('connectionConnect', { connectionId, type: ConnectionType.CONSOLE })
    })

    this.consoleManager.on('connectionDisconnect', (connectionId, reason) => {
      this.emit('connectionDisconnect', { connectionId, type: ConnectionType.CONSOLE, reason })
    })

    this.consoleManager.on('connectionError', (connectionId, error) => {
      this.emit('connectionError', { connectionId, type: ConnectionType.CONSOLE, error })
    })

    this.consoleManager.on('connectionStatusChange', (connectionId, status) => {
      this.emit('connectionStatusChange', { connectionId, type: ConnectionType.CONSOLE, status })
    })
  }

  /**
   * 设置RDP连接事件监听
   * @param rdpService RDP服务实例
   */
  private setupRdpConnectionEvents(rdpService: RdpService): void {
    rdpService.on('connect', (sessionInfo) => {
      this.emit('connectionConnect', {
        connectionId: rdpService.id,
        type: ConnectionType.RDP,
        sessionInfo
      })
    })

    rdpService.on('disconnect', (reason) => {
      this.emit('connectionDisconnect', {
        connectionId: rdpService.id,
        type: ConnectionType.RDP,
        reason
      })
    })

    rdpService.on('error', (error) => {
      this.emit('connectionError', {
        connectionId: rdpService.id,
        type: ConnectionType.RDP,
        error
      })
    })

    rdpService.on('statusChange', (status) => {
      this.emit('connectionStatusChange', {
        connectionId: rdpService.id,
        type: ConnectionType.RDP,
        status
      })
    })

    rdpService.on('reconnect', (attempt) => {
      this.emit('connectionReconnect', {
        connectionId: rdpService.id,
        type: ConnectionType.RDP,
        attempt
      })
    })
  }

  /**
   * 获取总连接数
   */
  private async getTotalConnectionCount(): Promise<number> {
    const consoleConnections = await this.consoleManager.listConnections()
    return consoleConnections.total + this.rdpConnections.size
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup().catch((error) => {
        this.log('error', '定期清理失败', {
          error: error instanceof Error ? error.message : '未知错误'
        })
      })
    }, this.options.cleanupInterval)
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param meta 元数据
   */
  private log(level: string, message: string, meta?: any): void {
    if (!this.options.enableLogging) {
      return
    }

    const logLevels = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = logLevels.indexOf(this.options.logLevel)
    const messageLevelIndex = logLevels.indexOf(level)

    if (messageLevelIndex >= currentLevelIndex) {
      const timestamp = new Date().toISOString()
      const logMessage = `[${timestamp}] [${level.toUpperCase()}] [UnifiedConnectionManager] ${message}`

      if (meta) {
        this.logger.log(logMessage, meta)
      } else {
        this.logger.log(logMessage)
      }
    }
  }
}

// 导出单例实例
let unifiedConnectionManagerInstance: UnifiedConnectionManager | null = null

/**
 * 获取统一连接管理器实例
 * @param options 管理器选项
 * @returns 管理器实例
 */
export function getUnifiedConnectionManager(options?: UnifiedConnectionManagerOptions): UnifiedConnectionManager {
  if (!unifiedConnectionManagerInstance) {
    unifiedConnectionManagerInstance = new UnifiedConnectionManager(options)
  }
  return unifiedConnectionManagerInstance
}

/**
 * 初始化统一连接管理器
 * @param options 管理器选项
 */
export function initializeUnifiedConnectionManager(options?: UnifiedConnectionManagerOptions): void {
  getUnifiedConnectionManager(options)
}

/**
 * 清理统一连接管理器
 */
export async function cleanupUnifiedConnectionManager(): Promise<void> {
  if (unifiedConnectionManagerInstance) {
    await unifiedConnectionManagerInstance.shutdown()
    unifiedConnectionManagerInstance = null
  }
}
