/**
 * RDP连接API控制器
 * 提供RDP连接服务的HTTP接口
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { ipcMain } from 'electron'
import { getRdpService } from './index'
import { RdpConnectionConfig, RdpManagerOptions, RdpMouseEvent, RdpKeyboardEvent, RdpDisplaySettings } from './types'

/**
 * RDP API控制器类
 * 处理来自渲染进程的RDP相关请求
 */
export class RdpApiController {
  private isInitialized: boolean = false
  private options: RdpManagerOptions

  /**
   * 构造函数
   * @param options RDP管理器选项
   */
  constructor(options: RdpManagerOptions = {}) {
    this.options = options
  }

  /**
   * 初始化API路由
   */
  public initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.registerIpcHandlers()
    this.isInitialized = true
    console.log('[RdpApi] RDP API已初始化')
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    // 移除IPC处理器
    this.removeIpcHandlers()

    this.isInitialized = false
    console.log('[RdpApi] RDP API已清理')
  }

  /**
   * 注册IPC处理器
   */
  private registerIpcHandlers(): void {
    const rdpService = getRdpService(this.options)

    // 建立RDP连接
    ipcMain.handle('rdp:connect', async (event, config: RdpConnectionConfig) => {
      try {
        this.validateConnectionConfig(config)
        const result = await rdpService.connect(config)
        return { success: true, data: result }
      } catch (error) {
        console.error('[RdpApi] RDP连接失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'RDP连接失败',
            code: 'RDP_CONNECTION_FAILED'
          }
        }
      }
    })

    // 断开RDP连接
    ipcMain.handle('rdp:disconnect', async (event, connectionId: string) => {
      try {
        if (!connectionId) {
          throw new Error('连接ID不能为空')
        }

        await rdpService.disconnect(connectionId)
        return { success: true }
      } catch (error) {
        console.error('[RdpApi] 断开RDP连接失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '断开RDP连接失败',
            code: 'RDP_DISCONNECT_FAILED'
          }
        }
      }
    })

    // 获取RDP连接状态
    ipcMain.handle('rdp:status', async (event, connectionId: string) => {
      try {
        if (!connectionId) {
          throw new Error('连接ID不能为空')
        }

        const status = await rdpService.getStatus(connectionId)
        return { success: true, data: status }
      } catch (error) {
        console.error('[RdpApi] 获取RDP状态失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '获取RDP状态失败',
            code: 'RDP_STATUS_FAILED'
          }
        }
      }
    })

    // 获取所有RDP连接列表
    ipcMain.handle('rdp:list', async (event) => {
      try {
        const connections = await rdpService.listConnections()
        return { success: true, data: connections }
      } catch (error) {
        console.error('[RdpApi] 获取RDP连接列表失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '获取RDP连接列表失败',
            code: 'RDP_LIST_FAILED'
          }
        }
      }
    })

    // 发送鼠标事件
    ipcMain.handle(
      'rdp:mouse-event',
      async (
        event,
        params: {
          connectionId: string
          mouseEvent: RdpMouseEvent
        }
      ) => {
        try {
          const { connectionId, mouseEvent } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          const connection = rdpService.getConnection(connectionId)
          if (!connection) {
            throw new Error('RDP连接不存在')
          }

          await connection.sendMouseEvent(mouseEvent)
          return { success: true }
        } catch (error) {
          console.error('[RdpApi] 发送鼠标事件失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '发送鼠标事件失败',
              code: 'RDP_MOUSE_EVENT_FAILED'
            }
          }
        }
      }
    )

    // 发送键盘事件
    ipcMain.handle(
      'rdp:keyboard-event',
      async (
        event,
        params: {
          connectionId: string
          keyboardEvent: RdpKeyboardEvent
        }
      ) => {
        try {
          const { connectionId, keyboardEvent } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          const connection = rdpService.getConnection(connectionId)
          if (!connection) {
            throw new Error('RDP连接不存在')
          }

          await connection.sendKeyboardEvent(keyboardEvent)
          return { success: true }
        } catch (error) {
          console.error('[RdpApi] 发送键盘事件失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '发送键盘事件失败',
              code: 'RDP_KEYBOARD_EVENT_FAILED'
            }
          }
        }
      }
    )

    // 发送剪贴板数据
    ipcMain.handle(
      'rdp:clipboard',
      async (
        event,
        params: {
          connectionId: string
          data: string
        }
      ) => {
        try {
          const { connectionId, data } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          const connection = rdpService.getConnection(connectionId)
          if (!connection) {
            throw new Error('RDP连接不存在')
          }

          await connection.sendClipboardData(data)
          return { success: true }
        } catch (error) {
          console.error('[RdpApi] 发送剪贴板数据失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '发送剪贴板数据失败',
              code: 'RDP_CLIPBOARD_FAILED'
            }
          }
        }
      }
    )

    // 获取屏幕截图
    ipcMain.handle(
      'rdp:screenshot',
      async (
        event,
        params: {
          connectionId: string
          region?: { x: number; y: number; width: number; height: number }
        }
      ) => {
        try {
          const { connectionId, region } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          const connection = rdpService.getConnection(connectionId)
          if (!connection) {
            throw new Error('RDP连接不存在')
          }

          const screenshot = await connection.getScreenshot(region)
          return { success: true, data: screenshot }
        } catch (error) {
          console.error('[RdpApi] 获取屏幕截图失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '获取屏幕截图失败',
              code: 'RDP_SCREENSHOT_FAILED'
            }
          }
        }
      }
    )

    // 调整显示设置
    ipcMain.handle(
      'rdp:resize-display',
      async (
        event,
        params: {
          connectionId: string
          settings: Partial<RdpDisplaySettings>
        }
      ) => {
        try {
          const { connectionId, settings } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          const connection = rdpService.getConnection(connectionId)
          if (!connection) {
            throw new Error('RDP连接不存在')
          }

          await connection.resizeDisplay(settings)
          return { success: true }
        } catch (error) {
          console.error('[RdpApi] 调整显示设置失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '调整显示设置失败',
              code: 'RDP_RESIZE_FAILED'
            }
          }
        }
      }
    )

    // 清理无效连接
    ipcMain.handle('rdp:cleanup', async (event) => {
      try {
        await rdpService.cleanup()
        return { success: true }
      } catch (error) {
        console.error('[RdpApi] 清理RDP连接失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '清理RDP连接失败',
            code: 'RDP_CLEANUP_FAILED'
          }
        }
      }
    })

    // 测试RDP连接
    ipcMain.handle('rdp:test', async (event, config: RdpConnectionConfig) => {
      try {
        this.validateConnectionConfig(config)

        // 创建临时连接进行测试
        const testConfig = {
          ...config,
          id: `test-rdp-${Date.now()}`,
          autoReconnect: false
        }

        const result = await rdpService.connect(testConfig)

        // 测试完成后立即断开
        setTimeout(async () => {
          try {
            await rdpService.disconnect(testConfig.id)
          } catch (error) {
            console.warn('[RdpApi] 测试RDP连接断开失败:', error)
          }
        }, 2000)

        return { success: true, data: result }
      } catch (error) {
        console.error('[RdpApi] RDP连接测试失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : 'RDP连接测试失败',
            code: 'RDP_TEST_FAILED'
          }
        }
      }
    })
  }

  /**
   * 移除IPC处理器
   */
  private removeIpcHandlers(): void {
    const handlers = [
      'rdp:connect',
      'rdp:disconnect',
      'rdp:status',
      'rdp:list',
      'rdp:mouse-event',
      'rdp:keyboard-event',
      'rdp:clipboard',
      'rdp:screenshot',
      'rdp:resize-display',
      'rdp:cleanup',
      'rdp:test'
    ]

    handlers.forEach((handler) => {
      ipcMain.removeAllListeners(handler)
    })
  }

  /**
   * 验证RDP连接配置
   * @param config 连接配置
   */
  private validateConnectionConfig(config: RdpConnectionConfig): void {
    if (!config) {
      throw new Error('RDP连接配置不能为空')
    }

    if (!config.id) {
      throw new Error('连接ID不能为空')
    }

    if (!config.assetId) {
      throw new Error('资产ID不能为空')
    }

    if (!config.name) {
      throw new Error('连接名称不能为空')
    }

    if (!config.host) {
      throw new Error('RDP主机地址不能为空')
    }

    if (!config.port || config.port <= 0 || config.port > 65535) {
      throw new Error('RDP端口必须在1-65535之间')
    }

    if (!config.username) {
      throw new Error('RDP用户名不能为空')
    }

    if (!config.displaySettings) {
      throw new Error('显示设置不能为空')
    }

    if (config.displaySettings.width <= 0 || config.displaySettings.height <= 0) {
      throw new Error('显示分辨率必须大于0')
    }

    if (!config.securitySettings) {
      throw new Error('安全设置不能为空')
    }
  }
}

// 导出单例实例
let rdpApiInstance: RdpApiController | null = null

/**
 * 获取RDP API控制器实例
 * @param options 管理器选项
 * @returns API控制器实例
 */
export function getRdpApiController(options?: RdpManagerOptions): RdpApiController {
  if (!rdpApiInstance) {
    rdpApiInstance = new RdpApiController(options)
  }
  return rdpApiInstance
}

/**
 * 初始化RDP API
 * @param options 管理器选项
 */
export function initializeRdpApi(options?: RdpManagerOptions): void {
  const controller = getRdpApiController(options)
  controller.initialize()
}

/**
 * 清理RDP API
 */
export async function cleanupRdpApi(): Promise<void> {
  if (rdpApiInstance) {
    await rdpApiInstance.cleanup()
    rdpApiInstance = null
  }
}
